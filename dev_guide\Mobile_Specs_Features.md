# DCBuyer Mobile Specifications - Core Features User Stories

## 📋 Executive Summary

This document provides detailed user stories and current system features for the three core functionalities of the DCBuyer system: **Buy**, **Transactions**, and **Customers**. These features form the foundation of the commodity trading and purchasing management platform.

**System Architecture**: CodeIgniter 4 with Supabase PostgreSQL  
**Current Status**: Web application with full CRUD operations  
**Target Platform**: Mobile-responsive web application  

---

## 🛒 BUY FEATURE

### Current System Implementation

**Status**: ✅ Fully Implemented (Admin Interface) | 🔄 Planned (Buyer Interface)  
**Controllers**: `TransactionController.php`, `AdminController.php`  
**Models**: `TransactionModel.php`, `MissionModel.php`, `UserModel.php`  
**Database**: `transactions` table, `mission` table, `users` table  

### System Architecture

The Buy feature operates through a mission-based authorization system where:
- Administrators assign buyers to specific missions
- Each mission contains commodity, location, and budget information
- Buyers can only create transactions for their assigned missions
- All transactions are validated against mission parameters

### Current Capabilities

#### Admin Interface (✅ Implemented)
- **Transaction Management**: Full CRUD operations for all transactions
- **Mission Assignment**: Assign buyers to specific commodity purchasing missions
- **Budget Tracking**: Monitor actual spending vs budgeted amounts
- **Authorization Control**: Validate buyer permissions for mission access
- **Transaction Validation**: Ensure commodity and location match mission requirements

#### Buyer Interface (🔄 Planned)
- **Mission Dashboard**: View assigned missions with budget tracking
- **Buy Interface**: Streamlined transaction creation for assigned missions
- **Transaction History**: Personal transaction tracking and history

### Detailed User Stories

#### Epic: Mission-Based Purchasing System

**As an Administrator:**
- I want to assign buyers to specific missions, so that purchasing responsibilities are clearly defined
- I want to set commodity and location constraints for missions, so that buyers purchase the right products in the right places
- I want to set budget limits for missions, so that spending is controlled
- I want to monitor all transactions across all missions, so that I can oversee purchasing activities
- I want to validate that buyers only create transactions for their assigned missions, so that authorization is enforced
- I want to see real-time budget tracking, so that I can prevent overspending

**As a Buyer:**
- I want to view my assigned missions, so that I know what commodities I need to purchase
- I want to see mission details including commodity, location, and budget, so that I can plan my purchases
- I want to create transactions for my assigned missions, so that I can record my purchases
- I want to see my remaining budget for each mission, so that I don't overspend
- I want to view my transaction history, so that I can track my purchasing activities
- I want to receive validation feedback if I try to create invalid transactions, so that I can correct errors

#### Epic: Transaction Creation Workflow

**As a Buyer:**
- I want to select from my assigned missions, so that I can choose which mission to purchase for
- I want the system to pre-populate commodity and location from the mission, so that I don't make errors
- I want to enter quantity and unit price, so that the system can calculate the total amount
- I want the system to automatically calculate payment amount (quantity × unit price), so that calculations are accurate
- I want to optionally assign a customer to the transaction, so that customer relationships can be tracked
- I want to add remarks to transactions, so that I can include additional context
- I want to receive confirmation when transactions are successfully created, so that I know the purchase was recorded

**As the System:**
- I want to validate that the buyer is assigned to the selected mission, so that unauthorized transactions are prevented
- I want to ensure the commodity matches the mission assignment, so that purchasing stays within scope
- I want to require location information, so that geographic data is captured
- I want to update mission actual amounts when transactions are created, so that budgets are tracked in real-time
- I want to generate unique transaction codes, so that each transaction can be uniquely identified

### Technical Implementation

#### Database Schema
```sql
-- Transactions table (renamed from buy_transactions)
transactions:
  - id (primary key)
  - transaction_code (unique, auto-generated)
  - user_id (foreign key to users)
  - mission_id (foreign key to mission)
  - commodity_id (foreign key to commodities)
  - customer_id (foreign key to customers, optional)
  - location_id (foreign key to locations)
  - quantity (decimal)
  - unit_price (decimal)
  - payment_amount (calculated: quantity × unit_price)
  - transaction_date (date)
  - remarks (text, optional)
  - status (pending, completed, cancelled)
  - timestamps and soft delete fields
```

#### API Endpoints
```
Admin Interface:
GET    /admin/transactions              # List all transactions
GET    /admin/transactions/create       # Create transaction form
POST   /admin/transactions              # Store new transaction
GET    /admin/transactions/{id}         # View transaction details
GET    /admin/transactions/{id}/edit    # Edit transaction form
PUT    /admin/transactions/{id}         # Update transaction
DELETE /admin/transactions/{id}         # Delete transaction

Buyer Interface (Planned):
GET    /buyer/dashboard                 # Buyer main dashboard
GET    /buyer/missions                  # List assigned missions
GET    /buyer/missions/{id}/buy         # Mission buy interface
POST   /buyer/missions/{id}/transactions # Create transaction for mission
GET    /buyer/transactions              # Transaction history
```

---

## 💳 TRANSACTIONS FEATURE

### Current System Implementation

**Status**: ✅ Fully Implemented  
**Controller**: `TransactionController.php`  
**Model**: `TransactionModel.php`  
**Views**: `admin/transactions/` (index, create, edit, show)  
**Database**: `transactions` table with full relationships  

### System Capabilities

#### Transaction Management
- **Comprehensive CRUD**: Create, read, update, delete transactions
- **Advanced Filtering**: Search by transaction code, user, mission, date range, status
- **Pagination**: Efficient handling of large transaction datasets
- **Statistics Dashboard**: Transaction summaries and performance metrics
- **Relationship Management**: Full integration with users, missions, commodities, customers, locations

#### Data Validation & Integrity
- **Mission Authorization**: Validate buyer permissions for mission access
- **Commodity Validation**: Ensure transaction commodity matches mission assignment
- **Automatic Calculations**: Payment amount = quantity × unit price
- **Budget Tracking**: Real-time updates to mission actual amounts
- **Unique Codes**: Auto-generated transaction codes for tracking

### Detailed User Stories

#### Epic: Transaction Lifecycle Management

**As an Administrator:**
- I want to view all transactions in a paginated list, so that I can efficiently browse large datasets
- I want to filter transactions by multiple criteria (user, mission, date, status), so that I can find specific transactions quickly
- I want to see transaction statistics and summaries, so that I can analyze purchasing performance
- I want to view detailed transaction information, so that I can review purchase details
- I want to edit transaction details when necessary, so that I can correct errors or update information
- I want to delete transactions when needed, so that I can remove invalid or duplicate entries
- I want to see transaction relationships (buyer, mission, commodity, customer, location), so that I have complete context

**As a Buyer:**
- I want to view my transaction history, so that I can track my purchasing activities
- I want to see transaction details including amounts and dates, so that I can review my purchases
- I want to filter my transactions by date or mission, so that I can find specific purchases
- I want to see transaction status, so that I know if purchases are pending or completed

#### Epic: Transaction Data Integrity

**As the System:**
- I want to validate all transaction data before saving, so that data integrity is maintained
- I want to automatically generate unique transaction codes, so that each transaction can be tracked
- I want to calculate payment amounts automatically, so that calculation errors are prevented
- I want to update mission budgets in real-time, so that spending tracking is accurate
- I want to maintain audit trails, so that transaction changes can be tracked
- I want to enforce referential integrity, so that transactions always link to valid entities

### Technical Features

#### Advanced Search & Filtering
- Transaction code search
- User/buyer filtering
- Mission filtering
- Date range filtering
- Status filtering (pending, completed, cancelled)
- Commodity filtering
- Customer filtering
- Location filtering

#### Performance Optimization
- Database indexing on key fields
- Efficient pagination with limit/offset
- Query optimization for complex joins
- Caching for frequently accessed data

---

## 👥 CUSTOMERS FEATURE

### Current System Implementation

**Status**: ✅ Fully Implemented  
**Controller**: `CustomersController.php`  
**Model**: `CustomerModel.php`  
**Views**: `admin/customers/` (index, create, edit, show)  
**Database**: `customers` table with location relationships  

### System Capabilities

#### Customer Management
- **Complete CRUD Operations**: Create, read, update, delete customers
- **Auto-Generated Codes**: Unique customer codes for identification
- **Location Integration**: Full integration with location hierarchy
- **Status Management**: Active/inactive customer status control
- **Transaction History**: View customer's transaction history
- **Advanced Search**: Search by name, code, phone, email

#### Data Management
- **Contact Information**: Name, phone, email, address
- **Geographic Data**: Integration with location hierarchy (country > province > district > LLG)
- **Status Tracking**: Active/inactive status management
- **Audit Trail**: Created/updated timestamps and user tracking

### Detailed User Stories

#### Epic: Customer Relationship Management

**As an Administrator:**
- I want to create new customer records, so that I can track customer relationships
- I want to view a list of all customers with pagination, so that I can efficiently manage large customer databases
- I want to search customers by name, code, phone, or email, so that I can quickly find specific customers
- I want to edit customer information, so that I can keep customer data up-to-date
- I want to view detailed customer profiles, so that I can see complete customer information
- I want to see customer transaction history, so that I can understand customer purchasing patterns
- I want to activate/deactivate customers, so that I can control which customers are available for transactions
- I want to assign customers to specific locations, so that geographic relationships are maintained

**As a Buyer:**
- I want to search for customers when creating transactions, so that I can assign purchases to the right customers
- I want to see customer contact information, so that I can communicate with customers if needed
- I want to view customer location information, so that I know where customers are located
- I want to see customer status, so that I know if customers are active for transactions

#### Epic: Customer Data Management

**As an Administrator:**
- I want customer codes to be automatically generated, so that each customer has a unique identifier
- I want to store complete customer contact information, so that customers can be reached when needed
- I want to integrate customers with the location hierarchy, so that geographic relationships are maintained
- I want to track when customers are created and updated, so that I have audit information
- I want to soft delete customers, so that historical transaction data is preserved
- I want to validate customer email addresses, so that contact information is accurate
- I want to ensure customer codes and emails are unique, so that duplicate customers are prevented

#### Epic: Customer Transaction Integration

**As the System:**
- I want to link transactions to customers optionally, so that customer relationships can be tracked
- I want to show customer transaction history, so that purchasing patterns can be analyzed
- I want to validate customer status before allowing transactions, so that only active customers can be used
- I want to maintain referential integrity between customers and transactions, so that data consistency is preserved

### Technical Features

#### Customer Code Generation
- Automatic unique code generation
- Configurable code format
- Collision detection and retry logic

#### Location Integration
- Full integration with location hierarchy
- Cascading dropdown support
- Geographic data validation

#### Search & Filtering
- Multi-field search (name, code, phone, email)
- Location-based filtering
- Status filtering (active/inactive)
- Date-based filtering (creation date)

#### Performance Features
- Efficient pagination
- Database indexing on search fields
- Optimized queries for customer lists
- Caching for frequently accessed data

---

## 🔗 FEATURE INTEGRATION

### Cross-Feature Relationships

#### Buy ↔ Transactions
- Buy feature creates transactions through mission-based workflow
- Transactions validate against mission assignments
- Budget tracking flows from transactions back to missions

#### Transactions ↔ Customers
- Transactions can optionally be assigned to customers
- Customer transaction history shows all related purchases
- Customer status affects transaction creation

#### Buy ↔ Customers
- Buyers can assign customers to transactions during purchase workflow
- Customer location data can influence mission location requirements

### Data Flow Architecture

```
Mission Assignment → Buyer Dashboard → Transaction Creation → Customer Assignment → Budget Update
     ↓                    ↓                    ↓                    ↓                ↓
  Admin Control    Buyer Interface    Transaction Record    Customer Tracking    Real-time Updates
```

---

## 📱 MOBILE CONSIDERATIONS

### Responsive Design
- Bootstrap 5 responsive framework
- Mobile-first design approach
- Touch-friendly interface elements
- Optimized for various screen sizes

### Performance Optimization
- Efficient data loading
- Minimal bandwidth usage
- Fast page load times
- Optimized images and assets

### User Experience
- Simplified navigation for mobile
- Large touch targets
- Clear visual hierarchy
- Intuitive workflow design

---

*Document Version: 1.0*  
*Last Updated: 2025-09-28*  
*System: DCBuyer - CodeIgniter 4 with Supabase PostgreSQL*
